{"actions": [], "autoname": "Pr-.YYYY.-.#####", "creation": "2025-09-16 14:57:32.225112", "doctype": "DocType", "engine": "InnoDB", "field_order": ["vehicle_info_section", "posting_date", "chassis_number", "availability_status", "availability_details", "column_break_tejs", "car_model", "model_year", "color", "auction_grade", "section_break_qrbv", "company_price", "customer_price", "sale_price", "vehicle_items", "total_quantity", "total_amount", "price_totals_section", "grand_total", "in_words", "amended_from", "state_info_tab", "state_info_section", "status"], "fields": [{"fieldname": "company_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Company Price", "reqd": 1}, {"fieldname": "customer_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Customer Price", "reqd": 1}, {"fieldname": "sale_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Sale Price", "read_only": 1}, {"columns": 5, "fieldname": "vehicle_items", "fieldtype": "Table", "label": "Vehicle Items", "options": "Vehicle Price Items"}, {"fieldname": "total_quantity", "fieldtype": "Float", "label": "Total Quantity", "precision": "2", "read_only": 1}, {"fieldname": "total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Amount", "non_negative": 1, "precision": "2", "read_only": 1}, {"fieldname": "price_totals_section", "fieldtype": "Section Break", "label": "Price Totals"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "non_negative": 1, "read_only": 1}, {"fieldname": "in_words", "fieldtype": "Small Text", "label": "In Words", "read_only": 1}, {"fieldname": "vehicle_info_section", "fieldtype": "Section Break", "label": "Vehicle Info"}, {"fieldname": "section_break_qrbv", "fieldtype": "Section Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "reqd": 1}, {"allow_on_submit": 1, "fieldname": "chassis_number", "fieldtype": "Link", "in_global_search": 1, "in_list_view": 1, "label": "<PERSON><PERSON><PERSON> Number", "no_copy": 1, "options": "Vehicle Entry", "reqd": 1}, {"fieldname": "column_break_tejs", "fieldtype": "Column Break"}, {"fetch_from": "chassis_number.car_model", "fieldname": "car_model", "fieldtype": "Data", "label": "Car Model", "read_only": 1}, {"fetch_from": "chassis_number.model_year", "fieldname": "model_year", "fieldtype": "Data", "label": "Model Year", "read_only": 1}, {"fetch_from": "chassis_number.color", "fieldname": "color", "fieldtype": "Data", "label": "Color", "read_only": 1}, {"fetch_from": "chassis_number.auction_grade", "fieldname": "auction_grade", "fieldtype": "Data", "label": "Auction Grade", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Vehicle Price", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "availability_status", "fieldtype": "Data", "in_filter": 1, "in_list_view": 1, "label": "Availability Status", "read_only": 1}, {"fieldname": "state_info_tab", "fieldtype": "Tab Break", "label": "State Info"}, {"fieldname": "state_info_section", "fieldtype": "Section Break", "label": "State Info"}, {"fieldname": "status", "fieldtype": "Select", "in_filter": 1, "in_list_view": 1, "label": "Status", "options": "\nDraft\nTo Availability and To Price\nTo Price\nPending Availability\nCompleted\nRollback", "read_only": 1}, {"fieldname": "availability_details", "fieldtype": "Small Text", "in_global_search": 1, "in_list_view": 1, "label": "Availability Details", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-09-18 15:15:30.127638", "modified_by": "Administrator", "module": "Vehicle Management", "name": "Vehicle Price", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}