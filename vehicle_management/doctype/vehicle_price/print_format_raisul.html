{%- from "templates/print_formats/standard_macros.html" import add_header -%}

<style>
    .print-container {
        border: 1px solid #000;
        padding: 20px;
        margin: 10px;
        font-family: Arial, sans-serif;
        min-height: 800px;
    }
    
    .header-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
    }
    
    .header-field {
        border: 1px solid #000;
        padding: 10px;
        min-width: 200px;
        font-weight: bold;
        background-color: #f9f9f9;
    }
    
    .section-title {
        font-weight: bold;
        margin: 20px 0 5px 0;
        font-size: 14px;
    }
    
    .details-box {
        border: 1px solid #000;
        padding: 15px;
        margin-bottom: 20px;
        min-height: 80px;
        background-color: #f9f9f9;
        line-height: 1.6;
    }
    
    .price-section {
        margin: 20px 0;
    }
    
    .price-row {
        display: flex;
        margin: 8px 0;
        align-items: center;
    }
    
    .price-label {
        font-weight: bold;
        width: 150px;
    }
    
    .price-colon {
        margin: 0 10px;
        font-weight: bold;
    }
    
    .price-value {
        font-weight: bold;
    }
    
    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }
    
    .items-table th,
    .items-table td {
        border: 1px solid #000;
        padding: 10px;
        text-align: center;
    }
    
    .items-table th {
        background-color: #f0f0f0;
        font-weight: bold;
    }
    
    .items-table td.text-left {
        text-align: left;
    }
    
    .totals-row {
        display: flex;
        justify-content: space-between;
        margin: 15px 0;
        font-weight: bold;
    }
    
    .final-totals {
        margin-top: 30px;
    }
    
    .final-row {
        display: flex;
        margin: 10px 0;
        align-items: center;
    }
    
    .final-label {
        font-weight: bold;
        width: 150px;
    }
</style>

<div class="print-container">
    {%- if not doc.get("print_heading") and not doc.get("select_print_heading")
        and doc.set("select_print_heading", _("Vehicle Price Receipt")) -%}{%- endif -%}
    {{ add_header(0, 1, doc, letter_head, no_letterhead, print_settings) }}
    
    <!-- Header Row -->
    <div class="header-row">
        <div class="header-field">
            ID: {{ doc.name }}
        </div>
        <div class="header-field">
            Date: {{ frappe.utils.format_date(doc.posting_date) }}
        </div>
    </div>
    
    <!-- Vehicle Details Section -->
    <div class="section-title">Vehicle Details</div>
    <div class="details-box">
        <strong>Chassis Number:</strong> {{ doc.chassis_number }}<br>
        <strong>Vehicle Name:</strong> {{ doc.vehicle_name or '' }}<br>
        <strong>Model Year:</strong> {{ doc.model_year or '' }}<br>
        <strong>Color:</strong> {{ doc.color or '' }}<br>
        <strong>Auction Grade:</strong> {{ doc.auction_grade or '' }}
    </div>
    
    <!-- Availability Details Section -->
    <div class="section-title">Availability Details</div>
    <div class="details-box">
        <strong>Status:</strong> {{ doc.availability_status or 'Not Available' }}
        {% if doc.availability_details %}
            <br><br>{{ doc.availability_details|replace('\n', '<br>')|safe }}
        {% endif %}
    </div>
    
    <!-- Price Section -->
    <div class="price-section">
        <div class="price-row">
            <div class="price-label">Company Price</div>
            <div class="price-colon">:</div>
            <div class="price-value">{{ doc.get_formatted("company_price") or "0.00" }}</div>
        </div>
        
        <div class="price-row">
            <div class="price-label">Customer Price</div>
            <div class="price-colon">:</div>
            <div class="price-value">{{ doc.get_formatted("customer_price") or "0.00" }}</div>
        </div>
        
        <div class="price-row">
            <div class="price-label">Sale Price</div>
            <div class="price-colon">:</div>
            <div class="price-value">{{ doc.get_formatted("sale_price") or "0.00" }}</div>
        </div>
    </div>
    
    <!-- Vehicle Items Section -->
    <div class="section-title">Vehicle Items</div>
    <table class="items-table">
        <thead>
            <tr>
                <th>Item</th>
                <th>Qty</th>
                <th>Rate</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            {% if doc.vehicle_items %}
                {% for item in doc.vehicle_items %}
                <tr>
                    <td class="text-left">{{ item.item_name or item.item_code or '' }}</td>
                    <td>{{ item.quantity or 0 }}</td>
                    <td>{{ item.get_formatted("rate") or "0.00" }}</td>
                    <td>{{ item.get_formatted("amount") or "0.00" }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="4" style="text-align: center; font-style: italic; padding: 20px;">No items added</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
    
    <!-- Totals Row -->
    <div class="totals-row">
        <div><strong>Total Qty: {{ doc.total_quantity or 0 }}</strong></div>
        <div><strong>Total Amount: {{ doc.get_formatted("total_amount") or "0.00" }}</strong></div>
    </div>
    
    <!-- Final Totals -->
    <div class="final-totals">
        <div class="final-row">
            <div class="final-label">Grand Total</div>
            <div class="price-colon">:</div>
            <div class="price-value">{{ doc.get_formatted("grand_total") or "0.00" }}</div>
        </div>
        
        <div class="final-row">
            <div class="final-label">In Words</div>
            <div class="price-colon">:</div>
            <div class="price-value">{{ doc.in_words or '' }}</div>
        </div>
    </div>
</div>