{"actions": [], "autoname": "field:auction_grade", "creation": "2025-09-16 11:46:59.048879", "doctype": "DocType", "engine": "InnoDB", "field_order": ["auction_grade"], "fields": [{"fieldname": "auction_grade", "fieldtype": "Data", "in_list_view": 1, "label": "Auction Grade", "reqd": 1, "unique": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-16 11:48:00.883987", "modified_by": "Administrator", "module": "Vehicle Management", "name": "Auction Grade", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}