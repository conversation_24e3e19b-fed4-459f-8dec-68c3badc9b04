{"actions": [], "autoname": "autoIncrement", "creation": "2025-09-16 15:06:40.951190", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item", "quantity", "rate", "amount"], "fields": [{"fieldname": "item", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "Vehicle Parts"}, {"fieldname": "quantity", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "reqd": 1}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "reqd": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-09-17 12:38:26.453221", "modified_by": "Administrator", "module": "Vehicle Management", "name": "Vehicle Price Items", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}