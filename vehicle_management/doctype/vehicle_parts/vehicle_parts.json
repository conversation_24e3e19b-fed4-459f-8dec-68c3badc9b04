{"actions": [], "autoname": "field:parts", "creation": "2025-09-16 15:11:41.538213", "doctype": "DocType", "engine": "InnoDB", "field_order": ["parts"], "fields": [{"fieldname": "parts", "fieldtype": "Data", "in_list_view": 1, "label": "Parts", "reqd": 1, "unique": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-16 15:18:58.542895", "modified_by": "Administrator", "module": "Vehicle Management", "name": "Vehicle Parts", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}